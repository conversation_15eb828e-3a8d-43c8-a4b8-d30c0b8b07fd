/**
 * handleSession函数 - 基于_worker.js的TransformStream修复版本
 * 完全按照_worker.js中的TransformStream使用方式实现
 */
export async function handleSession(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
    let tcpInterface = null;
    let writer = null;

    // 日志系统
    let address = '';
    let portWithRandomLog = '';
    const log = (info, event = '') => {
        console.log(`[${address}:${portWithRandomLog}] ${info}`, event || '');
    };

    // 按照_worker.js的方式创建TransformStream
    const hold = new TransformStream({
        transform(chunk, controller) {
            controller.enqueue(chunk);
        }
    });
    
    const upstreamReadable = hold.readable;
    const holdWriter = hold.writable.getWriter();

    // 处理早期数据 - 按照_worker.js的方式
    if (earlyHeader) {
        try {
            holdWriter.write(decodeBase64Url(earlyHeader)).catch(() => { });
        } catch (e) { }
    }

    // WebSocket事件处理 - 按照_worker.js的方式
    const handleMessage = (e) => {
        try {
            holdWriter.write(e.data);
        } catch (e) {
            // 忽略写入错误
        }
    };

    const handleClose = () => {
        try {
            holdWriter.close();
        } catch (e) {
            // 忽略关闭错误
        }
    };

    const handleError = (event) => {
        try {
            holdWriter.abort(event);
            if (server && server.close instanceof Function) {
                server.close(1013);
            }
        } catch (e) {
            // 忽略错误传播问题
        }
    };

    server.addEventListener("message", handleMessage);
    server.addEventListener("close", handleClose);
    server.addEventListener("error", handleError);

    // 异步处理主逻辑
    (async () => {
        try {
            // 获取第一个数据块进行协议解析 - 按照_worker.js的reader方式
            const reader = upstreamReadable.getReader();
            let firstChunk = null;
            
            try {
                const { value } = await reader.read();
                firstChunk = value;
            } finally {
                reader.releaseLock();
            }

            if (!firstChunk) {
                throw new Error('Client closed connection before sending header');
            }

            // 解析协议头
            const header = await parseProtocolHeader(firstChunk, server, protocolMode);
            
            address = header.addressRemote;
            portWithRandomLog = `${header.portRemote}--${Math.random()}`;
            log(`协议头解析成功: ${address}:${header.portRemote}`);

            // 建立TCP连接（含重试）- 按照_worker.js的逻辑
            try {
                tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
                await tcpInterface.opened;
                log(`首次连接成功: ${globalControllerConfig.connectMode}`);
            } catch (connectError) {
                log(`首次连接失败: ${connectError.message}`);
                try {
                    tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                    await tcpInterface.opened;
                    log(`重试连接成功: ${globalControllerConfig.retryMode}`);
                } catch (retryError) {
                    log(`重试连接失败: ${retryError.message}`);
                    throw retryError;
                }
            }

            writer = tcpInterface.writable.getWriter();

            // 发送初始数据
            if (header.rawClientData && header.rawClientData.byteLength > 0) {
                await writer.write(header.rawClientData);
                log(`发送初始数据: ${header.rawClientData.byteLength} bytes`);
            }

            // 上游数据传输：WebSocket → TCP - 按照_worker.js的pipeThrough方式
            upstreamReadable
                .pipeThrough(new TransformStream({
                    async transform(chunk, controller) {
                        try {
                            await writer.write(chunk);
                            log(`写入TCP数据: ${chunk.byteLength} bytes`);
                        } catch (e) {
                            if (e instanceof TypeError) controller.error();
                            throw e;
                        }
                    },
                    async flush() {
                        try {
                            await writer.close();
                            log('上游数据传输完成');
                        } catch (e) {
                            // 忽略关闭错误
                        }
                    }
                }))
                .pipeTo(new WritableStream())
                .catch((e) => { 
                    log(`上游传输错误: ${e.message}`);
                    throw e; 
                });

            // 下游数据传输：TCP → WebSocket - 按照_worker.js的pipeThrough方式
            await tcpInterface.readable.pipeThrough(new TransformStream({
                transform(chunk, controller) {
                    try {
                        server.send(chunk);
                        log(`发送TCP数据: ${chunk.byteLength} bytes`);
                        return;
                    } catch (e) {
                        if (e instanceof TypeError) controller.error();
                        throw e;
                    }
                },
            })).pipeTo(new WritableStream());

        } catch (error) {
            log(`handleSession处理错误: ${error.message}`);
            if (server.readyState === WebSocket.OPEN) {
                server.close(1011, 'Internal server error');
            }
            throw error;
        }
    })();

    return new Response(null, { status: 101, webSocket: client });
}

// 需要原_worker.js中的以下函数：
// - parseProtocolHeader
// - dial
// - decodeBase64Url
// - globalControllerConfig
