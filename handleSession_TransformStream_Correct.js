/**
 * handleSession函数 - 正确的TransformStream实现
 * 基于ReadableStream_Chain的可用逻辑，使用正确的TransformStream方式
 */
export async function handleSession(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
    let headerDone = false;
    let tcpWriter = null;
    let tcpReader = null;

    // 日志系统
    let address = '';
    let portWithRandomLog = '';
    const log = (info, event = '') => {
        console.log(`[${address}:${portWithRandomLog}] ${info}`, event || '');
    };

    // 创建TransformStream - 正确的方式
    const transformStream = new TransformStream({
        async transform(chunk, controller) {
            try {
                /* ---------- 首包：解析协议 + 建TCP ---------- */
                if (!headerDone) {
                    headerDone = true;
                    log('开始处理协议头');

                    // 1) 解析协议头 - 完全按照ReadableStream_Chain的方式
                    const header = await parseProtocolHeader(chunk, server, protocolMode);
                    
                    address = header.addressRemote;
                    portWithRandomLog = `${header.portRemote}--${Math.random()}`;
                    log(`协议头解析成功: ${address}:${header.portRemote}`);

                    // 2) 建立TCP连接（含重试）- 完全按照ReadableStream_Chain的方式
                    let tcpInterface;
                    try {
                        tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
                        await tcpInterface.opened;
                        log(`首次连接成功: ${globalControllerConfig.connectMode}`);
                    } catch (connectError) {
                        log(`首次连接失败: ${connectError.message}`);
                        try {
                            tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                            await tcpInterface.opened;
                            log(`重试连接成功: ${globalControllerConfig.retryMode}`);
                        } catch (retryError) {
                            log(`重试连接失败: ${retryError.message}`);
                            throw retryError;
                        }
                    }

                    // 3) 准备reader/writer - 完全按照ReadableStream_Chain的方式
                    tcpWriter = tcpInterface.writable.getWriter();
                    tcpReader = tcpInterface.readable.getReader();

                    // 4) 发送握手剩余数据 - 完全按照ReadableStream_Chain的方式
                    if (header.rawClientData && header.rawClientData.byteLength > 0) {
                        await tcpWriter.write(header.rawClientData);
                        log(`发送初始数据: ${header.rawClientData.byteLength} bytes`);
                    }

                    // 5) 单独异步任务：TCP → WebSocket - 完全按照ReadableStream_Chain的方式
                    (async () => {
                        try {
                            log('开始TCP到WebSocket数据传输');
                            while (true) {
                                const { value, done } = await tcpReader.read();
                                if (done) {
                                    log('TCP数据读取完成');
                                    break;
                                }
                                if (value && value.byteLength > 0) {
                                    server.send(value);
                                    log(`发送TCP数据: ${value.byteLength} bytes`);
                                }
                            }
                        } catch (e) {
                            log(`TCP到WebSocket传输错误: ${e.message}`);
                            try { 
                                server.close(1013, e.message); 
                            } catch {}
                        } finally {
                            try { 
                                tcpReader.releaseLock(); 
                            } catch {}
                        }
                    })();

                    return; // 首包处理完成
                }

                /* ---------- 后续数据：直接写TCP ---------- */
                if (tcpWriter) {
                    await tcpWriter.write(chunk);
                    log(`写入TCP数据: ${chunk.byteLength} bytes`);
                } else {
                    log('TCP连接未就绪');
                    throw new Error("TCP not ready");
                }
                
            } catch (error) {
                log(`Transform处理错误: ${error.message}`);
                throw error;
            }
        },

        flush() {
            log('TransformStream结束');
            try { 
                tcpWriter?.close(); 
            } catch {}
        }
    });

    // 正确的TransformStream使用方式：分别处理readable和writable
    
    // 1. 处理输入：WebSocket → TransformStream.writable
    const writer = transformStream.writable.getWriter();
    
    // 处理早期数据
    if (earlyHeader) {
        try {
            const earlyData = decodeBase64Url(earlyHeader);
            if (earlyData && earlyData.byteLength > 0) {
                await writer.write(earlyData);
                log(`处理早期数据: ${earlyData.byteLength} bytes`);
            }
        } catch (e) {
            log('早期数据解析失败', e.message);
        }
    }

    // WebSocket消息处理
    server.addEventListener("message", async ({ data }) => {
        try {
            await writer.write(data);
        } catch (e) {
            log('写入TransformStream失败', e.message);
        }
    });

    server.addEventListener("close", () => {
        log('WebSocket连接关闭');
        try {
            writer.close();
        } catch (e) {}
    });
    
    server.addEventListener("error", (e) => {
        log('WebSocket错误', e);
        try {
            writer.abort(e);
        } catch (err) {}
    });

    // 2. 处理输出：TransformStream.readable → 空的WritableStream（数据已在transform中处理）
    transformStream.readable
        .pipeTo(new WritableStream({
            write() {
                // 数据已在transform中处理完成
            },
            close() {
                log('TransformStream输出完成');
            },
            abort(reason) {
                log('TransformStream输出中止', reason);
            }
        }))
        .catch(e => {
            log(`TransformStream管道错误: ${e.message}`);
            try { 
                server.close(1013, e.message); 
            } catch {}
        });

    return new Response(null, { status: 101, webSocket: client });
}

// 需要原_worker.js中的以下函数：
// - parseProtocolHeader
// - dial
// - decodeBase64Url
// - globalControllerConfig
